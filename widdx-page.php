<?php

define('CLIENTAREA', true);

require __DIR__ . '/init.php';

$pageName = isset($_GET['page']) ? htmlspecialchars($_GET['page'], ENT_QUOTES, 'UTF-8') : 'default';
$action = isset($_GET['action']) ? htmlspecialchars($_GET['action'], ENT_QUOTES, 'UTF-8') : null;

$allowedPages = [
    'affiliate-program',
    'hosting-service-or-domain-transfer-tod',
    'hosting-terms-of-service-tos',
    'privacy-policy',
    'reseller-hosting-terms-of-service-tos',
    'terms-and-conditions',
    'offline',
    'blog',
    'whois-checker',
    'seo-analyzer',
    'payment-success',
    'dispute-resolution',
    'return-policy',
    'sitemap-generator',
    'squareup-payment',
    'terms-of-service',
];

if (!preg_match('/^[a-z0-9\-]+$/', $pageName) || !in_array($pageName, $allowedPages)) {
    $pageName = 'offline';
}

// Create a new client area object
$whmcs = $GLOBALS['whmcs'];
$ca = $whmcs->get_req_var("ca");

try {
    $ca->setPageTitle(ucwords(str_replace('-', ' ', $pageName)));

    $templateName = 'widdx-' . $pageName;
    $templatePath = __DIR__ . '/templates/widdx/frontend/pages/' . $templateName . '.tpl';

    if (file_exists($templatePath)) {
        if ($pageName === 'seo-analyzer') {
            require_once __DIR__ . '/templates/widdx/tools/widdx-seo-analyzer.php';
            $seoResult = handleSeoAnalyzer();

            if ($action === 'export_pdf' && isset($seoResult['analysisResult'])) {
                $pdfContent = generatePdfReport($seoResult);
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="seo_analysis_report.pdf"');
                echo $pdfContent;
                exit;
            }

            if (isset($seoResult['error'])) {
                $ca->assign('seoResult', ['error' => $seoResult['error']]);
            } else {
                $ca->assign('seoResult', $seoResult['analysisResult']);
                $ca->assign('seoScore', $seoResult['seoScore']);
                $ca->assign('faviconUrl', $seoResult['faviconUrl']);
                $ca->assign('url', $seoResult['url']);
            }
        } elseif ($pageName === 'whois-checker') {
            require __DIR__ . '/templates/widdx/app/widdx-whois-checker.php';
            $whoisResult = handleWhoisCheck();

            if ($action === 'check_whois' && !empty($_POST['domain'])) {
                // If it's an AJAX request, return only the WHOIS result
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    echo $whoisResult;
                    exit;
                }
            }

            $ca->assign('whoisResult', $whoisResult);
        }

        $ca->initPage();
        $ca->setTemplate('frontend/pages/' . $templateName);
    } else {
        $ca->setTemplate('frontend/pages/widdx-offline');
    }

    $ca->output();
} catch (Exception $e) {
    error_log($e->getMessage());
    $ca->setPageTitle('Error');
    $ca->initPage();
    $ca->setTemplate('frontend/pages/widdx-error');
    $ca->output();
}