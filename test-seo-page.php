<?php
/**
 * SEO Analyzer Page Test Script
 * Tests if the SEO analyzer page is working correctly
 */

// Basic checks
echo "<h1>SEO Analyzer Page Diagnostic</h1>";

// Check 1: File existence
echo "<h2>1. File Existence Check</h2>";
$files_to_check = [
    'widdx-page.php' => __DIR__ . '/widdx-page.php',
    'SEO Analyzer PHP' => __DIR__ . '/templates/widdx/tools/widdx-seo-analyzer.php',
    'SEO Analyzer Template' => __DIR__ . '/templates/widdx/frontend/pages/widdx-seo-analyzer.tpl',
    'Vendor Autoload' => __DIR__ . '/templates/widdx/tools/model/vendor/autoload.php',
    '.htaccess' => __DIR__ . '/.htaccess'
];

foreach ($files_to_check as $name => $path) {
    if (file_exists($path)) {
        echo "✅ $name: EXISTS<br>";
    } else {
        echo "❌ $name: MISSING ($path)<br>";
    }
}

// Check 2: URL Rewrite Test
echo "<h2>2. URL Rewrite Test</h2>";
$test_urls = [
    '/page/seo-analyzer',
    '/seo-analyzer',
    '/widdx-page.php?page=seo-analyzer'
];

foreach ($test_urls as $url) {
    echo "🔗 Test URL: <a href='$url' target='_blank'>$url</a><br>";
}

// Check 3: PHP Syntax Check
echo "<h2>3. PHP Syntax Check</h2>";
$php_files = [
    __DIR__ . '/widdx-page.php',
    __DIR__ . '/templates/widdx/tools/widdx-seo-analyzer.php'
];

foreach ($php_files as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "✅ " . basename($file) . ": Syntax OK<br>";
        } else {
            echo "❌ " . basename($file) . ": Syntax Error<br>";
            echo "<pre>$output</pre>";
        }
    }
}

// Check 4: Dependencies Check
echo "<h2>4. Dependencies Check</h2>";
$vendor_path = __DIR__ . '/templates/widdx/tools/model/vendor/autoload.php';
if (file_exists($vendor_path)) {
    try {
        require_once $vendor_path;
        echo "✅ Composer autoload: OK<br>";
        
        // Check specific classes
        $classes_to_check = [
            'GuzzleHttp\Client',
            'Symfony\Component\DomCrawler\Crawler',
            'Symfony\Component\Cache\Adapter\FilesystemAdapter'
        ];
        
        foreach ($classes_to_check as $class) {
            if (class_exists($class)) {
                echo "✅ $class: Available<br>";
            } else {
                echo "❌ $class: Missing<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ Autoload error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Composer autoload: Missing<br>";
}

// Check 5: WHMCS Integration
echo "<h2>5. WHMCS Integration Check</h2>";
if (defined('WHMCS')) {
    echo "✅ WHMCS: Loaded<br>";
} else {
    echo "❌ WHMCS: Not loaded<br>";
}

// Check 6: Template Variables
echo "<h2>6. Template System Check</h2>";
if (isset($GLOBALS['whmcs'])) {
    echo "✅ WHMCS Global: Available<br>";
} else {
    echo "❌ WHMCS Global: Missing<br>";
}

// Check 7: Permissions
echo "<h2>7. File Permissions Check</h2>";
$dirs_to_check = [
    __DIR__ . '/templates/widdx/tools',
    __DIR__ . '/templates/widdx/frontend/pages'
];

foreach ($dirs_to_check as $dir) {
    if (is_readable($dir)) {
        echo "✅ $dir: Readable<br>";
    } else {
        echo "❌ $dir: Not readable<br>";
    }
}

// Check 8: .htaccess Rules
echo "<h2>8. .htaccess Rules Check</h2>";
$htaccess_content = file_get_contents(__DIR__ . '/.htaccess');
if (strpos($htaccess_content, 'WIDDX Custom Page Rules') !== false) {
    echo "✅ Custom rewrite rules: Present<br>";
} else {
    echo "❌ Custom rewrite rules: Missing<br>";
}

// Check 9: Direct Access Test
echo "<h2>9. Direct Access Test</h2>";
echo "🔗 <a href='/widdx-page.php?page=seo-analyzer' target='_blank'>Direct access test</a><br>";

// Check 10: Error Log
echo "<h2>10. Recent Errors</h2>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    $recent_errors = shell_exec("tail -20 $error_log | grep -i 'seo\|widdx' || echo 'No recent SEO/WIDDX errors found'");
    echo "<pre>$recent_errors</pre>";
} else {
    echo "Error log not found or not configured<br>";
}

echo "<h2>Troubleshooting Steps</h2>";
echo "<ol>";
echo "<li>Try accessing: <a href='/widdx-page.php?page=seo-analyzer'>/widdx-page.php?page=seo-analyzer</a></li>";
echo "<li>If that works, the issue is with URL rewriting</li>";
echo "<li>If that doesn't work, check the PHP syntax and dependencies above</li>";
echo "<li>Check your web server error logs for more details</li>";
echo "<li>Ensure mod_rewrite is enabled on your server</li>";
echo "</ol>";
?>
