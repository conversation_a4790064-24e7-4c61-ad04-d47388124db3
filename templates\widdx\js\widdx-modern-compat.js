/*!
 * WHMCS WIDDX Theme - Modern JavaScript Compatibility Layer
 * Provides modern JavaScript alternatives and security enhancements
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function(window, document) {
    'use strict';

    // Modern compatibility layer for jQuery-dependent code
    const WiddxCompat = {
        
        // Enhanced DOM ready function
        ready: function(callback) {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', callback);
            } else {
                callback();
            }
        },

        // Secure AJAX wrapper with CSRF protection
        ajax: function(options) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            };

            // Add CSRF token if available
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                defaults.headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
            }

            const config = Object.assign({}, defaults, options);
            
            return fetch(config.url, {
                method: config.method,
                headers: config.headers,
                body: config.data ? JSON.stringify(config.data) : null,
                credentials: config.credentials
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX request failed:', error);
                if (config.error) {
                    config.error(error);
                }
                throw error;
            });
        },

        // Enhanced event delegation
        on: function(selector, event, handler) {
            document.addEventListener(event, function(e) {
                if (e.target.matches(selector) || e.target.closest(selector)) {
                    handler.call(e.target, e);
                }
            });
        },

        // Secure form validation
        validateForm: function(form) {
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
                
                // Email validation
                if (input.type === 'email' && input.value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(input.value)) {
                        input.classList.add('is-invalid');
                        isValid = false;
                    }
                }
            });
            
            return isValid;
        },

        // Secure local storage wrapper
        storage: {
            set: function(key, value) {
                try {
                    // Sanitize key
                    const sanitizedKey = key.replace(/[^a-zA-Z0-9_-]/g, '');
                    localStorage.setItem('widdx_' + sanitizedKey, JSON.stringify(value));
                    return true;
                } catch (e) {
                    console.warn('Failed to save to localStorage:', e);
                    return false;
                }
            },
            
            get: function(key) {
                try {
                    const sanitizedKey = key.replace(/[^a-zA-Z0-9_-]/g, '');
                    const item = localStorage.getItem('widdx_' + sanitizedKey);
                    return item ? JSON.parse(item) : null;
                } catch (e) {
                    console.warn('Failed to read from localStorage:', e);
                    return null;
                }
            },
            
            remove: function(key) {
                try {
                    const sanitizedKey = key.replace(/[^a-zA-Z0-9_-]/g, '');
                    localStorage.removeItem('widdx_' + sanitizedKey);
                    return true;
                } catch (e) {
                    console.warn('Failed to remove from localStorage:', e);
                    return false;
                }
            }
        },

        // Enhanced modal handling
        modal: {
            show: function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');
                    
                    // Focus trap
                    const focusableElements = modal.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );
                    if (focusableElements.length > 0) {
                        focusableElements[0].focus();
                    }
                }
            },
            
            hide: function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');
                }
            }
        },

        // Debounce function for performance
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Throttle function for performance
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // Enhanced error handling
        handleError: function(error, context = 'Unknown') {
            console.error(`[WIDDX Error - ${context}]:`, error);
            
            // Send error to logging service if available
            if (window.widdxErrorReporting && typeof window.widdxErrorReporting.log === 'function') {
                window.widdxErrorReporting.log({
                    error: error.message || error,
                    context: context,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                });
            }
        }
    };

    // Make WiddxCompat globally available
    window.WiddxCompat = WiddxCompat;

    // Initialize modern features when DOM is ready
    WiddxCompat.ready(function() {
        // Enhanced form handling
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!WiddxCompat.validateForm(this)) {
                    e.preventDefault();
                    return false;
                }
            });
        });

        // Enhanced accessibility
        document.querySelectorAll('[data-toggle="tooltip"]').forEach(element => {
            element.setAttribute('role', 'button');
            element.setAttribute('tabindex', '0');
        });

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            // Escape key closes modals
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    WiddxCompat.modal.hide(openModal.id);
                }
            }
        });

        console.log('WIDDX Modern Compatibility Layer initialized');
    });

})(window, document);
