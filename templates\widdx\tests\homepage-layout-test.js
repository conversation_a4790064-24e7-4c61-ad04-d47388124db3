/*!
 * WHMCS WIDDX Theme - Homepage Layout Test
 * Tests to verify the footer positioning fix
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function(window, document) {
    'use strict';

    const HomepageLayoutTest = {
        
        // Test if we're on the homepage
        isHomepage: function() {
            return document.body.classList.contains('homepage') || 
                   window.location.pathname === '/' || 
                   window.location.pathname.includes('index.php') ||
                   document.querySelector('.modern-hero-section') !== null;
        },

        // Test hero section positioning
        testHeroSection: function() {
            const heroSection = document.querySelector('.modern-hero-section');
            if (!heroSection) {
                return { passed: false, message: 'Hero section not found' };
            }

            const heroRect = heroSection.getBoundingClientRect();
            const heroTop = heroRect.top + window.scrollY;
            
            // Hero should be near the top of the page (allowing for header)
            if (heroTop < 200) {
                return { passed: true, message: 'Hero section positioned correctly at top' };
            } else {
                return { passed: false, message: `Hero section too far from top: ${heroTop}px` };
            }
        },

        // Test content sections positioning
        testContentSections: function() {
            const contentSelectors = [
                '.pricing-section',
                '.hosting-features-section', 
                '.productgroups-section',
                '.all-in-one-section',
                '.speed-section',
                '.tools-section'
            ];

            const heroSection = document.querySelector('.modern-hero-section');
            if (!heroSection) {
                return { passed: false, message: 'Hero section not found for comparison' };
            }

            const heroBottom = heroSection.getBoundingClientRect().bottom + window.scrollY;
            let foundSections = 0;
            let correctlyPositioned = 0;

            contentSelectors.forEach(selector => {
                const section = document.querySelector(selector);
                if (section) {
                    foundSections++;
                    const sectionTop = section.getBoundingClientRect().top + window.scrollY;
                    
                    // Content sections should be below the hero
                    if (sectionTop >= heroBottom - 100) { // Allow some overlap
                        correctlyPositioned++;
                    }
                }
            });

            if (foundSections === 0) {
                return { passed: false, message: 'No content sections found' };
            }

            const percentage = (correctlyPositioned / foundSections) * 100;
            return {
                passed: percentage >= 80,
                message: `${correctlyPositioned}/${foundSections} content sections positioned correctly (${percentage.toFixed(1)}%)`
            };
        },

        // Test footer positioning
        testFooterPosition: function() {
            const footer = document.querySelector('.widdx-footer, footer, [class*="footer"]');
            if (!footer) {
                return { passed: false, message: 'Footer not found' };
            }

            // Get all content sections
            const allSections = document.querySelectorAll('section, .homepage-section, [class*="section"]');
            let lastContentBottom = 0;

            allSections.forEach(section => {
                if (section !== footer && !section.contains(footer)) {
                    const sectionBottom = section.getBoundingClientRect().bottom + window.scrollY;
                    if (sectionBottom > lastContentBottom) {
                        lastContentBottom = sectionBottom;
                    }
                }
            });

            const footerTop = footer.getBoundingClientRect().top + window.scrollY;
            
            // Footer should be after all content sections
            if (footerTop >= lastContentBottom - 50) { // Allow small overlap
                return { passed: true, message: 'Footer positioned correctly after content' };
            } else {
                return { 
                    passed: false, 
                    message: `Footer appears too early. Footer top: ${footerTop}px, Last content bottom: ${lastContentBottom}px` 
                };
            }
        },

        // Test for layout overlaps
        testLayoutOverlaps: function() {
            const heroSection = document.querySelector('.modern-hero-section');
            const footer = document.querySelector('.widdx-footer, footer, [class*="footer"]');
            
            if (!heroSection || !footer) {
                return { passed: false, message: 'Hero section or footer not found' };
            }

            const heroRect = heroSection.getBoundingClientRect();
            const footerRect = footer.getBoundingClientRect();
            
            const heroBottom = heroRect.bottom + window.scrollY;
            const footerTop = footerRect.top + window.scrollY;
            
            // There should be content between hero and footer
            const gap = footerTop - heroBottom;
            
            if (gap > 100) { // At least 100px of content between hero and footer
                return { passed: true, message: `Good spacing between hero and footer: ${gap}px` };
            } else {
                return { passed: false, message: `Insufficient content between hero and footer: ${gap}px` };
            }
        },

        // Test responsive behavior
        testResponsiveLayout: function() {
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            const heroSection = document.querySelector('.modern-hero-section');
            if (!heroSection) {
                return { passed: false, message: 'Hero section not found' };
            }

            const heroHeight = heroSection.offsetHeight;
            
            // On mobile, hero shouldn't take up more than viewport height
            if (viewport.width <= 768 && heroHeight > viewport.height * 1.2) {
                return { 
                    passed: false, 
                    message: `Hero too tall on mobile: ${heroHeight}px vs viewport ${viewport.height}px` 
                };
            }

            return { passed: true, message: 'Responsive layout looks good' };
        },

        // Test CSS loading
        testCSSLoading: function() {
            const layoutFixCSS = document.querySelector('link[href*="homepage-layout-fix.css"]');
            
            if (!this.isHomepage()) {
                // CSS should not be loaded on non-homepage
                return { 
                    passed: !layoutFixCSS, 
                    message: layoutFixCSS ? 'Layout fix CSS loaded on non-homepage' : 'Layout fix CSS correctly not loaded on non-homepage'
                };
            } else {
                // CSS should be loaded on homepage
                return { 
                    passed: !!layoutFixCSS, 
                    message: layoutFixCSS ? 'Layout fix CSS loaded correctly' : 'Layout fix CSS not loaded on homepage'
                };
            }
        },

        // Run all layout tests
        runAllTests: function() {
            console.log('🏠 Starting Homepage Layout Tests...\n');
            
            if (!this.isHomepage()) {
                console.log('ℹ️ Not on homepage - running limited tests');
            }

            const tests = [
                { name: 'CSS Loading', test: () => this.testCSSLoading() },
                { name: 'Hero Section Position', test: () => this.testHeroSection() },
                { name: 'Content Sections Position', test: () => this.testContentSections() },
                { name: 'Footer Position', test: () => this.testFooterPosition() },
                { name: 'Layout Overlaps', test: () => this.testLayoutOverlaps() },
                { name: 'Responsive Layout', test: () => this.testResponsiveLayout() }
            ];

            let passed = 0;
            let failed = 0;

            tests.forEach(({ name, test }) => {
                try {
                    const result = test();
                    if (result.passed) {
                        console.log(`✅ ${name}: PASSED - ${result.message}`);
                        passed++;
                    } else {
                        console.error(`❌ ${name}: FAILED - ${result.message}`);
                        failed++;
                    }
                } catch (error) {
                    console.error(`💥 ${name}: ERROR - ${error.message}`);
                    failed++;
                }
            });

            console.log(`\n📊 Layout Test Results:`);
            console.log(`✅ Passed: ${passed}`);
            console.log(`❌ Failed: ${failed}`);
            console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

            return { passed, failed, total: passed + failed };
        },

        // Visual debug helper
        enableDebugMode: function() {
            document.body.classList.add('debug-layout');
            console.log('🔍 Debug mode enabled - sections will have colored borders');
        },

        disableDebugMode: function() {
            document.body.classList.remove('debug-layout');
            console.log('🔍 Debug mode disabled');
        }
    };

    // Make globally available
    window.HomepageLayoutTest = HomepageLayoutTest;

    // Auto-run tests if requested
    if (window.location.search.includes('test_layout=1') || 
        localStorage.getItem('widdx_test_layout') === 'true') {
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => HomepageLayoutTest.runAllTests(), 2000);
            });
        } else {
            setTimeout(() => HomepageLayoutTest.runAllTests(), 2000);
        }
    }

    // Add debug commands to console
    console.log('🏠 Homepage Layout Test loaded. Available commands:');
    console.log('   HomepageLayoutTest.runAllTests() - Run all layout tests');
    console.log('   HomepageLayoutTest.enableDebugMode() - Show section borders');
    console.log('   HomepageLayoutTest.disableDebugMode() - Hide section borders');

})(window, document);
