<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:30:07
  from 'C:\xampp\htdocs\templates\widdx\frontend\widgets\widdx-search-icon.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_688010ef4c8880_71485679',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9eaea3c66821f1f49914d8671885b5671b5bd8b9' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\frontend\\widgets\\widdx-search-icon.tpl',
      1 => 1726723194,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_688010ef4c8880_71485679 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- أيقونة البحث -->
<div class="main-nav-search mrl-1">
    <i class="fa fa-search" id="main-nav-search-btn"></i>
</div>

<!-- نافذة منبثقة للبحث -->
<div id="search-modal">
    <div class="search-content-area">
        <div class="search-close">
            <i class="fa fa-times" id="search-close-btn"></i>
        </div>
        <form method="post" action="<?php echo routePath('knowledgebase-search');?>
" class="search-form">
            <input type="text" name="search" class="search-input" placeholder="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"searchOurKnowledgebase"),$_smarty_tpl ) );?>
..." aria-label="Search">
            <button type="submit" class="search-submit">
                <i class="fas fa-search"></i>
            </button>
        </form>
        <div class="search-hint">Press Enter to search or Esc to close</div>
    </div>
</div>
<?php }
}
