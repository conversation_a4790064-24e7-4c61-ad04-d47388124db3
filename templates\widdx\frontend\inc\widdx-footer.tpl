</div>

</div>
{if !$inShoppingCart && $secondarySidebar->hasChildren()}
    <div class="d-lg-none sidebar sidebar-secondary">
        {include file="$template/includes/sidebar.tpl" sidebar=$secondarySidebar}
    </div>
{/if}
</div>
</section>
<footer id="footer" class="footer bg-dark">
  <div class="footer-top ptb-60">
    <div class="container">
      <div class="row">

        <!-- قسم العنوان -->
        <section class="col-lg-4 col-md-6 mb-4 mb-md-0 footer-address">
          <div class="footer-widget">
            <h6 class="text-white">Our Address</h6>
            <address class="mt-3 text-light">
              <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/logo/logo-white.svg"
                   alt="logo" class="img-fluid mb-3" style="max-width: 150px;">
              <p class="mb-2">
                <strong>WIDDX</strong> - Your Digital Partner<br>
                Offering tailored solutions for business growth: hosting, website design, app development, graphic design, digital marketing, and social media management.
              </p>
              <ul class="list-inline mb-0">
                {include file="$template/includes/social-accounts.tpl"}
              </ul>
              <p class="mb-1">
                <strong>Address:</strong> Jenin, Palestine
              </p>
              <p class="mb-3">
                <strong>Email:</strong> <a href="mailto:<EMAIL>" class="text-light"><EMAIL></a>
              </p>

            </address>
          </div>
        </section>

        <!-- قسم المنتجات -->
        <section class="col-lg-2 col-md-6 mb-4 mb-md-0 footer-products">
          <div class="footer-widget">
            <h6 class="text-white">Our Products</h6>
            <ul class="list-unstyled ww-footer-menu mt-3">
              <li><a href="{$WEB_ROOT}/store/web-hosting" class="text-light">Web Hosting</a></li>
              <li><a href="{$WEB_ROOT}/store/reseller-hosting" class="text-light">Reseller Hosting</a></li>
              <li><a href="{$WEB_ROOT}/store/business-hosting" class="text-light">Business Hosting</a></li>
              <li><a href="{$WEB_ROOT}/page/affiliate-program" class="text-light">Affiliate Program</a></li>
              <li><a href="{$WEB_ROOT}/contact.php" class="text-light">{lang key='contactus'}</a></li>
            </ul>
          </div>
        </section>

        <!-- قسم الأدوات المجانية -->
        <section class="col-lg-2 col-md-6 mb-4 mb-md-0 footer-tools">
          <div class="footer-widget">
            <h6 class="text-white">
              <i class="fas fa-tools me-2"></i>Free Tools
            </h6>
            <ul class="list-unstyled ww-footer-menu mt-3">
              <li>
                <a href="{$WEB_ROOT}/page/seo-analyzer" class="text-light">
                  <i class="fas fa-search-plus me-1"></i>SEO Analyzer
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/page/whois-checker" class="text-light">
                  <i class="fas fa-search me-1"></i>WHOIS Checker
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/page/sitemap-generator" class="text-light">
                  <i class="fas fa-sitemap me-1"></i>Sitemap Generator
                </a>
              </li>
            </ul>
          </div>
        </section>

        <!-- قسم الصفحات المخصصة -->
        <section class="col-lg-2 col-md-6 mb-4 mb-md-0 footer-pages">
          <div class="footer-widget">
            <h6 class="text-white">
              <i class="fas fa-file-alt me-2"></i>Resources
            </h6>
            <ul class="list-unstyled ww-footer-menu mt-3">
              <li>
                <a href="{$WEB_ROOT}/page/affiliate-program" class="text-light">
                  <i class="fas fa-handshake me-1"></i>Affiliate Program
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/knowledgebase.php" class="text-light">
                  <i class="fas fa-book me-1"></i>Knowledge Base
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/announcements.php" class="text-light">
                  <i class="fas fa-bullhorn me-1"></i>Announcements
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/serverstatus.php" class="text-light">
                  <i class="fas fa-server me-1"></i>Server Status
                </a>
              </li>
            </ul>
          </div>
        </section>

        <!-- قسم المساعدة والسياسات -->
        <section class="col-lg-2 col-md-6 mb-4 mb-md-0 footer-help">
          <div class="footer-widget">
            <h6 class="text-white">
              <i class="fas fa-question-circle me-2"></i>Legal & Support
            </h6>
            <ul class="list-unstyled ww-footer-menu mt-3">
              {if $acceptTOS}
              <li>
                <a href="{$tosURL}" target="_blank" class="text-light">
                  <i class="fas fa-file-contract me-1"></i>{lang key='ordertos'}
                </a>
              </li>
              {/if}
              <li>
                <a href="{$WEB_ROOT}/page/privacy-policy" class="text-light">
                  <i class="fas fa-shield-alt me-1"></i>Privacy Policy
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/page/terms-of-service" class="text-light">
                  <i class="fas fa-gavel me-1"></i>Terms of Service
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/page/return-policy" class="text-light">
                  <i class="fas fa-undo me-1"></i>Return Policy
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/page/dispute-resolution" class="text-light">
                  <i class="fas fa-balance-scale me-1"></i>Dispute Resolution
                </a>
              </li>
              <li>
                <a href="{$WEB_ROOT}/contact.php" class="text-light">
                  <i class="fas fa-envelope me-1"></i>Contact Support
                </a>
              </li>
            </ul>
          </div>
        </section>


        <!-- قسم وسائل الدفع -->
        <section class="col-lg-2 col-md-6 mb-4 mb-md-0 footer-payments">
          <div class="footer-widget">
            <h6 class="text-white">We Accept</h6>
            <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/icon-payments.png"
                 class="img-fluid mt-3" alt="payment methods" style="max-width: 250px;">
          </div>
        </section>

      </div><!-- /.row -->
    </div><!-- /.container -->
  </div><!-- /.footer-top -->

  <div class="footer-bottom py-3">
    <div class="container">
      <div class="row align-items-center justify-content-lg-between">
        <div class="col-lg-7">
          <p class="copyright {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}text-right{else}text-left{/if} mb-0 mt-0">
            {lang key="copyrightFooterNotice" year=$date_year company=$companyname}
          </p>
        </div>
        <div class="col-lg-5">
          <ul class="list-inline mb-0 {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}float-lg-left{else}float-lg-right{/if}">
            {if $languagechangeenabled && count($locales) > 1 || $currencies}
            <li class="list-inline-item">
              <button type="button" class="btn" data-toggle="modal" data-target="#modalChooseLanguage">
                <div class="d-inline-block align-middle">
                  <div class="iti-flag {if $activeLocale.countryCode === 'GB'}us{else}{$activeLocale.countryCode|lower}{/if}"></div>
                </div>
                {$activeLocale.localisedName} / {$activeCurrency.prefix}{$activeCurrency.code}
              </button>
            </li>
            {/if}
          </ul>
        </div>
      </div>
    </div>
  </div>
</footer>



<!-- PWA Install Prompt -->
<div class="pwa-install-prompt {if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}rtl{/if}"
    id="pwaInstallPrompt">
    <div class="pwa-install-content">
        <img src="{$WEB_ROOT}/templates/{$template}/frontend/assets/img/AppImages/192.png" alt="App Icon"
            class="pwa-install-icon">
        <div class="pwa-install-text">
            <h4>{$LANG.pwa_install_title|default:"Install Our App"}</h4>
            <p>{$LANG.pwa_install_message|default:"Install our app now for a better experience and offline access. Enjoy a fast and seamless experience! ðŸŒŸ"}
            </p>
        </div>
    </div>
    <div class="pwa-install-actions">
        <button class="pwa-install-button pwa-install-now" id="pwaInstallButton">
            {$LANG.pwa_install_now|default:"Install Now ðŸš€"}
        </button>
        <button class="pwa-install-button pwa-install-later" id="pwaInstallLater">
            {$LANG.pwa_install_later|default:"Maybe Later ðŸ•’"}
        </button>
    </div>
</div>

<!-- Language and Currency Modal -->
<form method="get" action="{$currentpagelinkback}">
    <div class="modal fade modal-localisation" id="modalChooseLanguage" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <!-- Ø²Ø± Ø§Ù„Ø¥ØºÙ„Ø§Ù‚ -->
                    <button type="button" class="btn-close" data-bs-dismiss="modal" data-dismiss="modal"
                        aria-label="Close"></button>

                    {if $languagechangeenabled && count($locales) > 1}
                        <h5 class="h5 pt-5 pb-3">{lang key='chooselanguage'}</h5>
                        <div class="row item-selector">
                            <input type="hidden" name="language" data-current="{$language}" value="{$language}" />
                            {foreach $locales as $locale}
                                <div class="col-4">
                                    <a href="#" class="item{if $language == $locale.language} active{/if}"
                                        data-value="{$locale.language}">
                                        {$locale.localisedName}
                                    </a>
                                </div>
                            {/foreach}
                        </div>
                    {/if}
                    {if !$loggedin && $currencies}
                        <p class="h5 pt-5 pb-3">{lang key='choosecurrency'}</p>
                        <div class="row item-selector">
                            <input type="hidden" name="currency" data-current="{$activeCurrency.id}" value="">
                            {foreach $currencies as $selectCurrency}
                                <div class="col-4">
                                    <a href="#" class="item{if $activeCurrency.id == $selectCurrency.id} active{/if}"
                                        data-value="{$selectCurrency.id}">
                                        {$selectCurrency.prefix} {$selectCurrency.code}
                                    </a>
                                </div>
                            {/foreach}
                        </div>
                    {/if}
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-secondary">{lang key='apply'}</button>
                </div>
            </div>
        </div>
    </div>
</form>

{if !$loggedin && $adminLoggedIn}
    <a href="{$WEB_ROOT}/logout.php?returntoadmin=1" class="btn btn-return-to-admin" data-toggle="tooltip" data-placement="bottom" title="{if $adminMasqueradingAsClient}{lang key='adminmasqueradingasclient'} {lang key='logoutandreturntoadminarea'}{else}{lang key='adminloggedin'} {lang key='returntoadminarea'}{/if}">
        <i class="fas fa-redo-alt"></i>
        <span>{lang key="admin.returnToAdmin"}</span>
    </a>
{/if}
<!-- Core Scripts -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/jquery-3.6.0.min.js"></script>
<script>
    jQuery.noConflict(true);
</script>
<script src="{assetPath file='swiper-bundle.min.js'}?v={$versionHash}"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/widdx-script.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap.bundle.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap-slider.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/owl.carousel.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/magnific-popup.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/validator.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/hs.megamenu.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/app.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/progressbar.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/pwa-install.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/sidebar.js"></script>

{if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
<!-- RTL Support -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/rtl-support.js"></script>
{/if}

<!-- Performance Optimizer (Load early for better performance) -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/performance-optimizer.js"></script>

<!-- Domain Checker Fix -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/domain-checker-fix.js"></script>

<!-- Domain Submission Fix (Enhanced) -->
<script src="{$WEB_ROOT}/templates/{$template}/domain-submission-fix.js"></script>

<!-- Error Handling -->
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/profile-image-fix.js"></script>

<!-- Owl Carousel Initialization -->
<script>
    $(".owl-carousel").owlCarousel({
        loop: true,
        margin: 10,
        nav: true,
        dots: true,
        autoplay: true,
        autoplayTimeout: 5000,
        autoplayHoverPause: true,
        animateOut: 'fadeOut',
        animateIn: 'fadeIn',
        responsive: {
            0: { items: 1 },
            400: { items: 2 },
            800: { items: 3 },
            1000: { items: 4 }
        }
    });
</script>

{* JavaScript Assets *}
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/jquery-3.6.0.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap.bundle.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/app.min.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/theme-system.js"></script>
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/performance-optimizer.js"></script>

{* RTL Support *}
{if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/rtl-support.js"></script>
{/if}

{* PWA Support *}
<script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/pwa-install.js"></script>

{$footeroutput}
</body>
</html>

