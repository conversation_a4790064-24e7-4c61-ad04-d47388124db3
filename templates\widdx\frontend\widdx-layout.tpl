<!doctype html>
<html lang="{if $language}{$language}{else}en{/if}" dir="{if $language == 'arabic' || $language == 'hebrew' || $language == 'farsi'}rtl{else}ltr{/if}" data-bs-theme="light">
<!-- Immediate theme application -->
<script>
    (function() {
        var savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
        }
    })();
</script>

<head>
    <meta charset="{$charset}" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Title tag is defined in widdx-head-seo.tpl -->

    <!-- Preconnect to external resources for better performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">

    <!-- Preload critical CSS -->
    <link rel="preload" href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/variables.css" as="style">
    <link rel="preload" href="{$WEB_ROOT}/templates/{$template}/css/theme.css" as="style">

    <!-- CSS Variables (Load first for performance) -->
    <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/variables.css" rel="stylesheet">

    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet">

    <!-- Fallback font for better performance -->
    <style>
        /* Font fallback to prevent layout shift */
        body {
            font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
    </style>
    {include file="$template/frontend/inc/widdx-head.tpl"}
    {if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
        <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/rtl/bootstrap-rtl.css" rel="stylesheet">
        <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/rtl/widdx-rtl.css" rel="stylesheet">
    {/if}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css">
    <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/pwa-install.css" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/{$template}/frontend/assets/css/animated-background.css" rel="stylesheet">

    <!-- CSRF Token Meta Tag -->
    <meta name="csrf-token" content="{if $token}{$token}{else}{$csrfToken}{/if}">

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="SAMEORIGIN">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">

    {$headoutput}
    <!-- Canonical URL is defined in widdx-head-seo.tpl -->
</head>

<body class="primary-bg-color" data-bs-theme="light" data-phone-cc-input="{$phoneNumberInputStyle}">
    <script>
        // Ensure body has the same theme as html
        document.body.setAttribute('data-bs-theme', document.documentElement.getAttribute('data-bs-theme'));
    </script>
    {$headeroutput}

    <!-- Header Section -->
    {include file="$template/frontend/inc/widdx-header.tpl"}
    {include file="$template/includes/network-issues-notifications.tpl"}


    <!-- Hero Section -->
    {if $templatefile == 'homepage'}
        {include file="$template/frontend/sections/hero/widdx-modern-hero.tpl"}
        {if $registerdomainenabled || $transferdomainenabled}
        {/if}
    {/if}
    <!-- Main Content -->
    <section id="main-body animated-background">
        <div class="{if !$skipMainBodyContainer}container{/if}">
            <div class="row">

                {if !$inShoppingCart && ($primarySidebar->hasChildren() || $secondarySidebar->hasChildren())}
                    <div class="col-lg-4 col-xl-3">
                        <div class="sidebar">
                            {include file="$template/includes/sidebar.tpl" sidebar=$primarySidebar}
                        </div>
                        {if !$inShoppingCart && $secondarySidebar->hasChildren()}
                            <div class="d-none d-lg-block sidebar">
                                {include file="$template/includes/sidebar.tpl" sidebar=$secondarySidebar}
                            </div>
                        {/if}
                    </div>
                {/if}

                <div class="{if !$inShoppingCart && ($primarySidebar->hasChildren() || $secondarySidebar->hasChildren())}col-lg-8 col-xl-9{else}col-12{/if} primary-content">

                    {* Flash Messages *}
                    {include file="$template/includes/flashmessage.tpl"}

                    {* Main Content Area *}
                    <div class="content-wrapper">
                        {$templatefile}
                    </div>

                </div>
            </div>
        </div>
    </section>

    {* Footer Section *}
    {include file="$template/frontend/inc/widdx-footer.tpl"}

    {* JavaScript Assets *}
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/jquery-3.6.0.min.js"></script>
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/vendors/bootstrap.bundle.min.js"></script>
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/app.min.js"></script>
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/theme-system.js"></script>
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/performance-optimizer.js"></script>

    {* RTL Support *}
    {if ($language == 'arabic' || $language == 'hebrew' || $language == 'farsi')}
        <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/rtl-support.js"></script>
    {/if}

    {* PWA Support *}
    <script src="{$WEB_ROOT}/templates/{$template}/frontend/assets/js/pwa-install.js"></script>

    {$footeroutput}
</body>
</html>
