/*!
 * WHMCS WIDDX Theme - Comprehensive Test Suite
 * Tests for theme functionality, security, and performance
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function(window, document) {
    'use strict';

    const WiddxTests = {
        results: {
            passed: 0,
            failed: 0,
            tests: []
        },

        // Test runner
        run: function(testName, testFunction) {
            try {
                const result = testFunction();
                if (result) {
                    this.results.passed++;
                    this.results.tests.push({ name: testName, status: 'PASSED', message: 'Test completed successfully' });
                    console.log(`✅ ${testName}: PASSED`);
                } else {
                    this.results.failed++;
                    this.results.tests.push({ name: testName, status: 'FAILED', message: 'Test assertion failed' });
                    console.error(`❌ ${testName}: FAILED`);
                }
            } catch (error) {
                this.results.failed++;
                this.results.tests.push({ name: testName, status: 'ERROR', message: error.message });
                console.error(`💥 ${testName}: ERROR - ${error.message}`);
            }
        },

        // Security Tests
        testCSRFTokenPresence: function() {
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            return csrfToken && csrfToken.getAttribute('content').length > 0;
        },

        testSecurityHeaders: function() {
            const requiredHeaders = [
                'X-Content-Type-Options',
                'X-Frame-Options', 
                'X-XSS-Protection'
            ];
            
            return requiredHeaders.every(header => {
                const meta = document.querySelector(`meta[http-equiv="${header}"]`);
                return meta !== null;
            });
        },

        testXSSProtection: function() {
            if (typeof WiddxSecurity === 'undefined') {
                return false;
            }
            
            const testInput = '<script>alert("xss")</script>';
            const sanitized = WiddxSecurity.sanitizeInput(testInput);
            return !sanitized.includes('<script>');
        },

        // Performance Tests
        testResourcePreloading: function() {
            const preloadLinks = document.querySelectorAll('link[rel="preload"]');
            return preloadLinks.length > 0;
        },

        testDNSPrefetch: function() {
            const dnsPrefetchLinks = document.querySelectorAll('link[rel="dns-prefetch"]');
            return dnsPrefetchLinks.length > 0;
        },

        testFontFallbacks: function() {
            const styles = document.querySelectorAll('style');
            let hasFallback = false;
            
            styles.forEach(style => {
                if (style.textContent.includes('font-family') && 
                    style.textContent.includes('sans-serif')) {
                    hasFallback = true;
                }
            });
            
            return hasFallback;
        },

        // Compatibility Tests
        testModernCompatLayer: function() {
            return typeof WiddxCompat !== 'undefined' && 
                   typeof WiddxCompat.ready === 'function' &&
                   typeof WiddxCompat.ajax === 'function';
        },

        testFormValidation: function() {
            if (typeof WiddxCompat === 'undefined') {
                return false;
            }
            
            // Create a test form
            const testForm = document.createElement('form');
            const requiredInput = document.createElement('input');
            requiredInput.type = 'text';
            requiredInput.required = true;
            requiredInput.value = '';
            testForm.appendChild(requiredInput);
            
            const isValid = WiddxCompat.validateForm(testForm);
            return !isValid; // Should return false for empty required field
        },

        testLocalStorageWrapper: function() {
            if (typeof WiddxCompat === 'undefined') {
                return false;
            }
            
            const testKey = 'test_key';
            const testValue = 'test_value';
            
            WiddxCompat.storage.set(testKey, testValue);
            const retrieved = WiddxCompat.storage.get(testKey);
            WiddxCompat.storage.remove(testKey);
            
            return retrieved === testValue;
        },

        // Theme Functionality Tests
        testThemeSwitcher: function() {
            const themeSwitcher = document.querySelector('[data-bs-theme]');
            return themeSwitcher !== null;
        },

        testResponsiveDesign: function() {
            const viewport = document.querySelector('meta[name="viewport"]');
            return viewport && viewport.getAttribute('content').includes('width=device-width');
        },

        testAccessibility: function() {
            const ariaElements = document.querySelectorAll('[aria-label], [role]');
            return ariaElements.length > 0;
        },

        // Payment Gateway Tests
        testLahzaIntegration: function() {
            // Check if Lahza payment scripts are loaded
            const lahzaScript = document.querySelector('script[src*="lahza"]');
            const lahzaContainer = document.getElementById('lahza-payment-container');
            
            return lahzaScript !== null || lahzaContainer !== null || 
                   typeof window.lahzaPaymentHandler !== 'undefined';
        },

        // Template File Tests
        testTemplateStructure: function() {
            // Check if essential template elements are present
            const essentialElements = [
                'head',
                'body',
                'meta[charset]',
                'title'
            ];
            
            return essentialElements.every(selector => {
                return document.querySelector(selector) !== null;
            });
        },

        testCSSLoading: function() {
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            return stylesheets.length > 0;
        },

        testJavaScriptLoading: function() {
            const scripts = document.querySelectorAll('script[src]');
            return scripts.length > 0;
        },

        // Error Handling Tests
        testErrorHandling: function() {
            if (typeof WiddxCompat === 'undefined') {
                return false;
            }
            
            try {
                WiddxCompat.handleError(new Error('Test error'), 'Test Context');
                return true;
            } catch (e) {
                return false;
            }
        },

        // Run all tests
        runAllTests: function() {
            console.log('🚀 Starting WIDDX Theme Test Suite...\n');
            
            // Security Tests
            console.log('🔒 Running Security Tests...');
            this.run('CSRF Token Presence', () => this.testCSRFTokenPresence());
            this.run('Security Headers', () => this.testSecurityHeaders());
            this.run('XSS Protection', () => this.testXSSProtection());
            
            // Performance Tests
            console.log('\n⚡ Running Performance Tests...');
            this.run('Resource Preloading', () => this.testResourcePreloading());
            this.run('DNS Prefetch', () => this.testDNSPrefetch());
            this.run('Font Fallbacks', () => this.testFontFallbacks());
            
            // Compatibility Tests
            console.log('\n🔧 Running Compatibility Tests...');
            this.run('Modern Compatibility Layer', () => this.testModernCompatLayer());
            this.run('Form Validation', () => this.testFormValidation());
            this.run('Local Storage Wrapper', () => this.testLocalStorageWrapper());
            
            // Theme Functionality Tests
            console.log('\n🎨 Running Theme Functionality Tests...');
            this.run('Theme Switcher', () => this.testThemeSwitcher());
            this.run('Responsive Design', () => this.testResponsiveDesign());
            this.run('Accessibility', () => this.testAccessibility());
            
            // Payment Gateway Tests
            console.log('\n💳 Running Payment Gateway Tests...');
            this.run('Lahza Integration', () => this.testLahzaIntegration());
            
            // Template Tests
            console.log('\n📄 Running Template Structure Tests...');
            this.run('Template Structure', () => this.testTemplateStructure());
            this.run('CSS Loading', () => this.testCSSLoading());
            this.run('JavaScript Loading', () => this.testJavaScriptLoading());
            
            // Error Handling Tests
            console.log('\n🚨 Running Error Handling Tests...');
            this.run('Error Handling', () => this.testErrorHandling());
            
            // Display results
            this.displayResults();
        },

        // Display test results
        displayResults: function() {
            console.log('\n📊 Test Results Summary:');
            console.log(`✅ Passed: ${this.results.passed}`);
            console.log(`❌ Failed: ${this.results.failed}`);
            console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
            
            if (this.results.failed > 0) {
                console.log('\n❌ Failed Tests:');
                this.results.tests.filter(test => test.status !== 'PASSED').forEach(test => {
                    console.log(`   • ${test.name}: ${test.message}`);
                });
            }
            
            console.log('\n🎉 WIDDX Theme Test Suite Complete!');
            
            return {
                passed: this.results.passed,
                failed: this.results.failed,
                total: this.results.passed + this.results.failed,
                successRate: (this.results.passed / (this.results.passed + this.results.failed)) * 100,
                details: this.results.tests
            };
        }
    };

    // Make WiddxTests globally available
    window.WiddxTests = WiddxTests;

    // Auto-run tests when page loads (only in development/testing)
    if (window.location.search.includes('run_tests=1') || 
        localStorage.getItem('widdx_auto_test') === 'true') {
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => WiddxTests.runAllTests(), 1000);
            });
        } else {
            setTimeout(() => WiddxTests.runAllTests(), 1000);
        }
    }

})(window, document);
