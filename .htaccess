
### BEGIN - WIDDX Custom Page Rules ###
<IfModule mod_rewrite.c>
RewriteEngine on
RewriteBase /

# WIDDX Custom Page Routing - Must come BEFORE WHMCS rules
RewriteRule ^page/([a-z0-9\-]+)/?$ widdx-page.php?page=$1 [QSA,L]
RewriteRule ^page/([a-z0-9\-]+)/([a-z0-9\-]+)/?$ widdx-page.php?page=$1&action=$2 [QSA,L]

# SEO Analyzer specific routes
RewriteRule ^seo-analyzer/?$ widdx-page.php?page=seo-analyzer [QSA,L]
RewriteRule ^seo-analyzer/([a-z0-9\-]+)/?$ widdx-page.php?page=seo-analyzer&action=$1 [QSA,L]

# Other tool routes
RewriteRule ^whois-checker/?$ widdx-page.php?page=whois-checker [QSA,L]
RewriteRule ^sitemap-generator/?$ widdx-page.php?page=sitemap-generator [QSA,L]
</IfModule>
### END - WIDDX Custom Page Rules ###

### BEGIN - WHMCS managed rules - DO NOT EDIT BETWEEN WHMCS MARKERS ###
<IfModule mod_rewrite.c>
# RewriteEngine already on from above
# RewriteBase already set from above

# Redirect directories to an address with slash
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^(.+[^/])$  $1/ [R]

# Send all remaining (routable paths) through index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
# Determine and use the actual base
RewriteCond $0#%{REQUEST_URI} ([^#]*)#(.*)\1$
RewriteRule ^.*$ %2index.php [QSA,L]
</IfModule>
### END - WHMCS managed rules - DO NOT EDIT BETWEEN WHMCS MARKERS ###
