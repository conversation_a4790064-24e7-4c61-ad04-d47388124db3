/*!
 * WHMCS WIDDX Theme - Twitter Section Styles
 * Professional Twitter feed integration styling
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

/* ===================================
   TWITTER SECTION STYLES
   =================================== */

.twitter-section {
    background: var(--section-bg, #f8f9fa);
    position: relative;
    overflow: hidden;
}

/* Dark theme support */
[data-bs-theme="dark"] .twitter-section {
    background: var(--section-bg-dark, #1a1a1a);
}

.twitter-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(29, 161, 242, 0.05) 0%, 
        rgba(29, 161, 242, 0.02) 50%, 
        transparent 100%);
    pointer-events: none;
}

.twitter-section .container {
    position: relative;
    z-index: 2;
}

/* Section heading */
.twitter-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--heading-color, #2c3e50);
    margin-bottom: 1rem;
}

[data-bs-theme="dark"] .twitter-section .section-title {
    color: var(--heading-color-dark, #ffffff);
}

.twitter-section .section-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted, #6c757d);
    margin-bottom: 0;
}

[data-bs-theme="dark"] .twitter-section .section-subtitle {
    color: var(--text-muted-dark, #adb5bd);
}

/* Twitter feed container */
.twitter-feed-container {
    background: var(--card-bg, #ffffff);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color, #e9ecef);
    min-height: 400px;
    position: relative;
}

[data-bs-theme="dark"] .twitter-feed-container {
    background: var(--card-bg-dark, #2d3748);
    border-color: var(--border-color-dark, #4a5568);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Loading state */
.twitter-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.twitter-loading .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.twitter-loading p {
    font-size: 1rem;
    margin: 0;
}

/* Fallback state */
.twitter-fallback {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.twitter-fallback .alert {
    border: none;
    background: rgba(29, 161, 242, 0.1);
    color: var(--text-color, #2c3e50);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
}

[data-bs-theme="dark"] .twitter-fallback .alert {
    background: rgba(29, 161, 242, 0.2);
    color: var(--text-color-dark, #ffffff);
}

.twitter-fallback .fab.fa-twitter {
    color: #1da1f2 !important;
    display: block;
    margin: 0 auto 1rem;
}

/* Twitter widget customization */
.twitter-feed-container iframe {
    border-radius: 8px !important;
    border: none !important;
    box-shadow: none !important;
}

/* Follow button */
.twitter-section .btn-outline-primary {
    border: 2px solid #1da1f2;
    color: #1da1f2;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.twitter-section .btn-outline-primary:hover {
    background: #1da1f2;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(29, 161, 242, 0.3);
}

.twitter-section .btn-outline-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(29, 161, 242, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .twitter-section {
        padding: 3rem 0;
    }
    
    .twitter-section .section-title {
        font-size: 2rem;
    }
    
    .twitter-feed-container {
        padding: 1.5rem;
        margin: 0 1rem;
    }
    
    .twitter-section .btn-outline-primary {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .twitter-section .section-title {
        font-size: 1.75rem;
    }
    
    .twitter-feed-container {
        padding: 1rem;
        margin: 0 0.5rem;
        min-height: 300px;
    }
    
    .twitter-loading,
    .twitter-fallback {
        min-height: 150px;
    }
}

/* Animation enhancements */
.twitter-feed-container {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out 0.3s forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .twitter-feed-container {
        animation: none;
        opacity: 1;
        transform: none;
    }
    
    .twitter-section .btn-outline-primary:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .twitter-section {
        background: var(--high-contrast-bg, #ffffff);
    }
    
    .twitter-feed-container {
        border: 2px solid var(--high-contrast-border, #000000);
    }
    
    .twitter-section .section-title {
        color: var(--high-contrast-text, #000000);
    }
}

/* Print styles */
@media print {
    .twitter-section {
        background: white !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .twitter-feed-container {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
    
    .twitter-section .btn-outline-primary {
        display: none;
    }
}

/* Focus management for accessibility */
.twitter-feed-container:focus-within {
    outline: 2px solid var(--focus-color, #1da1f2);
    outline-offset: 2px;
}

/* Error state styling */
.twitter-error {
    color: var(--danger-color, #dc3545);
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

[data-bs-theme="dark"] .twitter-error {
    background: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
}

/* Success state for loaded content */
.twitter-feed-loaded .twitter-feed-container {
    border-color: rgba(40, 167, 69, 0.3);
}

.twitter-feed-loaded .twitter-feed-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1da1f2, #1991db);
    border-radius: 12px 12px 0 0;
}
