/*!
 * WHMCS WIDDX Theme - Twitter & Footer Fix Test
 * Tests to verify Twitter section and duplicate footer fixes
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function(window, document) {
    'use strict';

    const TwitterFooterTest = {
        
        // Test Twitter section functionality
        testTwitterSection: function() {
            const twitterSection = document.querySelector('.twitter-section');
            const twitterContainer = document.querySelector('#twitterFeedOutput');
            
            if (!twitterSection && !twitterContainer) {
                return { 
                    passed: true, 
                    message: 'Twitter section not enabled (this is normal if no Twitter username is configured)' 
                };
            }

            if (!twitterSection) {
                return { 
                    passed: false, 
                    message: 'Twitter container found but section wrapper missing' 
                };
            }

            if (!twitterContainer) {
                return { 
                    passed: false, 
                    message: 'Twitter section found but feed container missing' 
                };
            }

            return { 
                passed: true, 
                message: 'Twitter section structure is correct' 
            };
        },

        // Test Twitter JavaScript loading
        testTwitterJS: function() {
            const twitterScript = document.querySelector('script[src*="twitter.js"]');
            
            if (!document.querySelector('.twitter-section')) {
                return { 
                    passed: true, 
                    message: 'Twitter JS not loaded (Twitter section not present)' 
                };
            }

            if (!twitterScript) {
                return { 
                    passed: false, 
                    message: 'Twitter section present but twitter.js not loaded' 
                };
            }

            // Check if TwitterFeed object is available
            if (typeof window.TwitterFeed !== 'undefined') {
                return { 
                    passed: true, 
                    message: 'Twitter JS loaded and TwitterFeed object available' 
                };
            } else {
                return { 
                    passed: false, 
                    message: 'Twitter JS loaded but TwitterFeed object not available' 
                };
            }
        },

        // Test Twitter CSS loading
        testTwitterCSS: function() {
            const twitterCSS = document.querySelector('link[href*="twitter-section.css"]');
            
            if (!document.querySelector('.twitter-section')) {
                return { 
                    passed: true, 
                    message: 'Twitter CSS not loaded (Twitter section not present)' 
                };
            }

            if (!twitterCSS) {
                return { 
                    passed: false, 
                    message: 'Twitter section present but CSS not loaded' 
                };
            }

            return { 
                passed: true, 
                message: 'Twitter CSS loaded correctly' 
            };
        },

        // Test for duplicate footers
        testDuplicateFooters: function() {
            const footers = document.querySelectorAll('footer, .widdx-footer, [class*="footer"]');
            const bodyCloseTags = document.documentElement.outerHTML.match(/<\/body>/gi);
            const htmlCloseTags = document.documentElement.outerHTML.match(/<\/html>/gi);
            
            let issues = [];
            
            // Check for multiple footer elements
            if (footers.length > 1) {
                issues.push(`Found ${footers.length} footer elements (should be 1)`);
            }
            
            // Check for multiple body close tags
            if (bodyCloseTags && bodyCloseTags.length > 1) {
                issues.push(`Found ${bodyCloseTags.length} </body> tags (should be 1)`);
            }
            
            // Check for multiple html close tags
            if (htmlCloseTags && htmlCloseTags.length > 1) {
                issues.push(`Found ${htmlCloseTags.length} </html> tags (should be 1)`);
            }

            if (issues.length === 0) {
                return { 
                    passed: true, 
                    message: 'No duplicate footers or closing tags found' 
                };
            } else {
                return { 
                    passed: false, 
                    message: 'Duplicate elements found: ' + issues.join(', ') 
                };
            }
        },

        // Test footer positioning
        testFooterPosition: function() {
            const footer = document.querySelector('footer, .widdx-footer, [class*="footer"]');
            
            if (!footer) {
                return { 
                    passed: false, 
                    message: 'No footer found on page' 
                };
            }

            const footerRect = footer.getBoundingClientRect();
            const footerTop = footerRect.top + window.scrollY;
            const documentHeight = document.documentElement.scrollHeight;
            const footerHeight = footerRect.height;
            
            // Footer should be near the bottom of the document
            const distanceFromBottom = documentHeight - (footerTop + footerHeight);
            
            if (distanceFromBottom < 100) { // Allow some margin
                return { 
                    passed: true, 
                    message: `Footer positioned correctly at bottom (${distanceFromBottom}px from end)` 
                };
            } else {
                return { 
                    passed: false, 
                    message: `Footer too far from bottom: ${distanceFromBottom}px from end` 
                };
            }
        },

        // Test Twitter feed functionality (if enabled)
        testTwitterFeedFunctionality: function() {
            const twitterContainer = document.querySelector('#twitterFeedOutput');
            
            if (!twitterContainer) {
                return { 
                    passed: true, 
                    message: 'Twitter feed not enabled' 
                };
            }

            const hasContent = twitterContainer.innerHTML.trim().length > 0;
            const hasLoadingState = twitterContainer.querySelector('.twitter-loading');
            const hasFallbackState = twitterContainer.querySelector('.twitter-fallback');
            const hasTwitterWidget = twitterContainer.querySelector('iframe[id*="twitter-widget"]');
            
            if (hasTwitterWidget) {
                return { 
                    passed: true, 
                    message: 'Twitter widget loaded successfully' 
                };
            } else if (hasLoadingState) {
                return { 
                    passed: true, 
                    message: 'Twitter feed in loading state (this is normal)' 
                };
            } else if (hasFallbackState) {
                return { 
                    passed: true, 
                    message: 'Twitter feed showing fallback content (this is normal if feed unavailable)' 
                };
            } else if (hasContent) {
                return { 
                    passed: true, 
                    message: 'Twitter feed has content' 
                };
            } else {
                return { 
                    passed: false, 
                    message: 'Twitter feed container is empty' 
                };
            }
        },

        // Test responsive design
        testResponsiveDesign: function() {
            const twitterSection = document.querySelector('.twitter-section');
            
            if (!twitterSection) {
                return { 
                    passed: true, 
                    message: 'Twitter section not present - responsive test skipped' 
                };
            }

            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            const sectionStyles = window.getComputedStyle(twitterSection);
            const containerStyles = window.getComputedStyle(twitterSection.querySelector('.container'));
            
            // Check if section has proper responsive padding
            const hasPadding = parseInt(sectionStyles.paddingTop) > 0 && parseInt(sectionStyles.paddingBottom) > 0;
            
            if (!hasPadding) {
                return { 
                    passed: false, 
                    message: 'Twitter section missing responsive padding' 
                };
            }

            return { 
                passed: true, 
                message: `Responsive design looks good (viewport: ${viewport.width}x${viewport.height})` 
            };
        },

        // Run all tests
        runAllTests: function() {
            console.log('🐦 Starting Twitter & Footer Fix Tests...\n');

            const tests = [
                { name: 'Twitter Section Structure', test: () => this.testTwitterSection() },
                { name: 'Twitter JavaScript Loading', test: () => this.testTwitterJS() },
                { name: 'Twitter CSS Loading', test: () => this.testTwitterCSS() },
                { name: 'Duplicate Footers Check', test: () => this.testDuplicateFooters() },
                { name: 'Footer Position', test: () => this.testFooterPosition() },
                { name: 'Twitter Feed Functionality', test: () => this.testTwitterFeedFunctionality() },
                { name: 'Responsive Design', test: () => this.testResponsiveDesign() }
            ];

            let passed = 0;
            let failed = 0;

            tests.forEach(({ name, test }) => {
                try {
                    const result = test();
                    if (result.passed) {
                        console.log(`✅ ${name}: PASSED - ${result.message}`);
                        passed++;
                    } else {
                        console.error(`❌ ${name}: FAILED - ${result.message}`);
                        failed++;
                    }
                } catch (error) {
                    console.error(`💥 ${name}: ERROR - ${error.message}`);
                    failed++;
                }
            });

            console.log(`\n📊 Twitter & Footer Test Results:`);
            console.log(`✅ Passed: ${passed}`);
            console.log(`❌ Failed: ${failed}`);
            console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

            return { passed, failed, total: passed + failed };
        },

        // Manual Twitter feed reload
        reloadTwitterFeed: function() {
            if (typeof window.TwitterFeed !== 'undefined') {
                console.log('🔄 Reloading Twitter feed...');
                window.TwitterFeed.loadTwitterFeed();
            } else {
                console.warn('TwitterFeed object not available');
            }
        }
    };

    // Make globally available
    window.TwitterFooterTest = TwitterFooterTest;

    // Auto-run tests if requested
    if (window.location.search.includes('test_twitter=1') || 
        localStorage.getItem('widdx_test_twitter') === 'true') {
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => TwitterFooterTest.runAllTests(), 3000);
            });
        } else {
            setTimeout(() => TwitterFooterTest.runAllTests(), 3000);
        }
    }

    // Add debug commands to console
    console.log('🐦 Twitter & Footer Test loaded. Available commands:');
    console.log('   TwitterFooterTest.runAllTests() - Run all tests');
    console.log('   TwitterFooterTest.reloadTwitterFeed() - Reload Twitter feed');

})(window, document);
