/**
 * WHMCS WIDDX Theme Twitter Feed JavaScript
 * Handles Twitter feed loading and display
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function($, window, document) {
    'use strict';

    const TwitterFeed = {
        
        // Configuration
        config: {
            feedContainer: '#twitterFeedOutput',
            loadingImage: '/assets/img/loading.gif',
            retryAttempts: 3,
            retryDelay: 2000,
            timeout: 10000
        },

        // Initialize Twitter feed
        init: function() {
            console.log('🐦 Initializing Twitter feed...');
            this.loadTwitterFeed();
        },

        // Load Twitter feed via AJAX
        loadTwitterFeed: function() {
            const self = this;
            const $container = $(this.config.feedContainer);
            
            if (!$container.length) {
                console.warn('Twitter feed container not found');
                return;
            }

            // Show loading state
            this.showLoading($container);

            // Make AJAX request to get Twitter feed
            $.ajax({
                url: 'index.php?rp=/announcements/twitterfeed',
                method: 'POST',
                timeout: this.config.timeout,
                dataType: 'html',
                success: function(data) {
                    self.handleSuccess(data, $container);
                },
                error: function(xhr, status, error) {
                    self.handleError(xhr, status, error, $container);
                }
            });
        },

        // Handle successful response
        handleSuccess: function(data, $container) {
            console.log('✅ Twitter feed loaded successfully');
            
            if (data && data.trim()) {
                $container.html(data);
                this.enhanceTwitterWidget();
            } else {
                this.showFallback($container, 'No tweets available at the moment.');
            }
        },

        // Handle error response
        handleError: function(xhr, status, error, $container) {
            console.error('❌ Twitter feed failed to load:', status, error);
            
            let errorMessage = 'Unable to load Twitter feed.';
            
            if (status === 'timeout') {
                errorMessage = 'Twitter feed request timed out.';
            } else if (xhr.status === 404) {
                errorMessage = 'Twitter feed endpoint not found.';
            } else if (xhr.status >= 500) {
                errorMessage = 'Server error loading Twitter feed.';
            }
            
            this.showFallback($container, errorMessage);
        },

        // Show loading state
        showLoading: function($container) {
            const loadingHtml = `
                <div class="twitter-loading text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading latest tweets...</p>
                </div>
            `;
            $container.html(loadingHtml);
        },

        // Show fallback content
        showFallback: function($container, message) {
            const fallbackHtml = `
                <div class="twitter-fallback text-center py-4">
                    <div class="alert alert-info">
                        <i class="fab fa-twitter text-primary mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-2">${message}</p>
                        <p class="mb-0">
                            <a href="https://twitter.com/${window.twitterUsername || 'widdx'}" 
                               target="_blank" 
                               class="btn btn-primary btn-sm">
                                <i class="fab fa-twitter me-1"></i>
                                Follow us on Twitter
                            </a>
                        </p>
                    </div>
                </div>
            `;
            $container.html(fallbackHtml);
        },

        // Enhance Twitter widget with custom styling and functionality
        enhanceTwitterWidget: function() {
            const self = this;
            
            // Wait for Twitter widget to load
            this.waitForTwitterWidget(function() {
                self.customizeTwitterWidget();
                self.removeRetweets();
                self.addTwitterObserver();
            });
        },

        // Wait for Twitter widget to be available
        waitForTwitterWidget: function(callback, attempts = 0) {
            const maxAttempts = 20;
            const widget = document.getElementById('twitter-widget-0');
            
            if (widget && widget.contentWindow) {
                callback();
            } else if (attempts < maxAttempts) {
                setTimeout(() => {
                    this.waitForTwitterWidget(callback, attempts + 1);
                }, 500);
            } else {
                console.warn('Twitter widget failed to load after maximum attempts');
            }
        },

        // Customize Twitter widget appearance
        customizeTwitterWidget: function() {
            try {
                const widget = document.getElementById('twitter-widget-0');
                if (!widget || !widget.contentWindow) return;

                const widgetDoc = widget.contentWindow.document;
                const head = widgetDoc.querySelector('head');
                
                if (head) {
                    const style = widgetDoc.createElement('style');
                    style.textContent = `
                        .timeline-Tweet-text {
                            font-size: 16px !important;
                            line-height: 22px !important;
                            margin-bottom: 8px !important;
                        }
                        .timeline-Tweet {
                            border-radius: 8px !important;
                            margin-bottom: 12px !important;
                        }
                        .timeline-Tweet-author {
                            margin-bottom: 8px !important;
                        }
                    `;
                    head.appendChild(style);
                }
            } catch (error) {
                console.warn('Could not customize Twitter widget:', error);
            }
        },

        // Remove retweets from timeline
        removeRetweets: function() {
            try {
                const widget = $('#twitter-widget-0');
                if (widget.length) {
                    widget.contents()
                        .find('.timeline-Tweet--isRetweet')
                        .parent('li')
                        .remove();
                }
            } catch (error) {
                console.warn('Could not remove retweets:', error);
            }
        },

        // Add observer to monitor Twitter widget changes
        addTwitterObserver: function() {
            try {
                const widget = document.getElementById('twitter-widget-0');
                if (!widget || !widget.contentWindow) return;

                const widgetDoc = widget.contentWindow.document;
                const timeline = widgetDoc.querySelector('.timeline-TweetList');
                
                if (timeline && window.MutationObserver) {
                    const observer = new MutationObserver(() => {
                        this.removeRetweets();
                    });
                    
                    observer.observe(timeline, {
                        childList: true,
                        subtree: true
                    });
                }
            } catch (error) {
                console.warn('Could not add Twitter observer:', error);
            }
        },

        // Retry loading with exponential backoff
        retryLoad: function(attempt = 1) {
            if (attempt > this.config.retryAttempts) {
                console.error('Max retry attempts reached for Twitter feed');
                return;
            }

            const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
            console.log(`Retrying Twitter feed load (attempt ${attempt}) in ${delay}ms...`);
            
            setTimeout(() => {
                this.loadTwitterFeed();
            }, delay);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize if Twitter feed container exists
        if ($(TwitterFeed.config.feedContainer).length) {
            TwitterFeed.init();
        }
    });

    // Make TwitterFeed globally available for debugging
    window.TwitterFeed = TwitterFeed;

    // Legacy compatibility - expose functions that might be called elsewhere
    window.removeRetweets = function() {
        TwitterFeed.removeRetweets();
    };

    window.addTwitterWidgetObserverWhenNodeAvailable = function() {
        TwitterFeed.enhanceTwitterWidget();
    };

})(jQuery, window, document);
