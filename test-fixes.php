<!DOCTYPE html>
<html>
<head>
    <title>WIDDX Theme Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 3px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔧 WIDDX Theme Fixes Test Page</h1>
    
    <div class="test-section info">
        <h2>📋 Test Summary</h2>
        <p>This page tests the fixes applied to the WIDDX theme:</p>
        <ul>
            <li>✅ Duplicate footer fix</li>
            <li>✅ URL rewriting for custom pages</li>
            <li>✅ SEO analyzer page routing</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🌐 URL Routing Tests</h2>
        <p>Test these URLs to verify the routing is working:</p>
        
        <h3>SEO Analyzer:</h3>
        <a href="/page/seo-analyzer" class="test-link" target="_blank">/page/seo-analyzer</a>
        <a href="/seo-analyzer" class="test-link" target="_blank">/seo-analyzer</a>
        <a href="/widdx-page.php?page=seo-analyzer" class="test-link" target="_blank">Direct URL</a>
        
        <h3>Other Tools:</h3>
        <a href="/page/whois-checker" class="test-link" target="_blank">/page/whois-checker</a>
        <a href="/whois-checker" class="test-link" target="_blank">/whois-checker</a>
        <a href="/page/sitemap-generator" class="test-link" target="_blank">/page/sitemap-generator</a>
    </div>

    <div class="test-section">
        <h2>🔍 File Checks</h2>
        <?php
        $files_to_check = [
            'widdx-page.php' => 'Main page handler',
            'templates/widdx/frontend/pages/widdx-seo-analyzer.tpl' => 'SEO Analyzer template',
            'templates/widdx/tools/widdx-seo-analyzer.php' => 'SEO Analyzer logic',
            'templates/widdx/frontend/inc/widdx-footer.tpl' => 'Footer template',
            '.htaccess' => 'URL rewrite rules'
        ];
        
        foreach ($files_to_check as $file => $description) {
            $exists = file_exists($file);
            $class = $exists ? 'success' : 'error';
            $status = $exists ? '✅ EXISTS' : '❌ MISSING';
            echo "<div class='$class'>$status - $description ($file)</div>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>⚙️ .htaccess Rules Check</h2>
        <?php
        if (file_exists('.htaccess')) {
            $htaccess_content = file_get_contents('.htaccess');
            $has_custom_rules = strpos($htaccess_content, 'WIDDX Custom Page Rules') !== false;
            $rules_before_whmcs = strpos($htaccess_content, 'WIDDX Custom Page Rules') < strpos($htaccess_content, 'WHMCS managed rules');
            
            if ($has_custom_rules && $rules_before_whmcs) {
                echo "<div class='success'>✅ Custom rewrite rules are properly configured and positioned before WHMCS rules</div>";
            } elseif ($has_custom_rules) {
                echo "<div class='error'>❌ Custom rewrite rules exist but may be positioned incorrectly</div>";
            } else {
                echo "<div class='error'>❌ Custom rewrite rules are missing</div>";
            }
        } else {
            echo "<div class='error'>❌ .htaccess file not found</div>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🐛 Footer Duplication Check</h2>
        <p>Check the main homepage to verify only one footer appears:</p>
        <a href="/" class="test-link" target="_blank">Check Homepage</a>
        
        <h3>What to look for:</h3>
        <ul>
            <li>Only ONE footer section at the bottom</li>
            <li>No duplicate content</li>
            <li>Proper page structure</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📊 Server Information</h2>
        <?php
        echo "<div class='info'>";
        echo "<strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
        echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
        echo "<strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
        echo "<strong>Current URL:</strong> " . $_SERVER['REQUEST_URI'] . "<br>";
        
        // Check if mod_rewrite is loaded
        if (function_exists('apache_get_modules')) {
            $modules = apache_get_modules();
            $mod_rewrite = in_array('mod_rewrite', $modules);
            echo "<strong>mod_rewrite:</strong> " . ($mod_rewrite ? '✅ Enabled' : '❌ Disabled') . "<br>";
        } else {
            echo "<strong>mod_rewrite:</strong> Cannot detect (function not available)<br>";
        }
        echo "</div>";
        ?>
    </div>

    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <ol>
            <li><strong>Test URL routing:</strong> Click the links above to test if custom pages work</li>
            <li><strong>Check homepage:</strong> Verify only one footer appears</li>
            <li><strong>Test SEO analyzer:</strong> Try analyzing a website URL</li>
            <li><strong>Clear caches:</strong> Clear browser cache and any server caches</li>
        </ol>
    </div>

    <div class="test-section info">
        <h2>📞 Troubleshooting</h2>
        <p>If issues persist:</p>
        <ul>
            <li>Check web server error logs</li>
            <li>Verify mod_rewrite is enabled</li>
            <li>Test direct URLs first (widdx-page.php?page=...)</li>
            <li>Clear all caches (browser, server, WHMCS)</li>
            <li>Check file permissions</li>
        </ul>
    </div>

    <script>
        // Add click tracking for test links
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function() {
                console.log('Testing URL:', this.href);
            });
        });
        
        // Auto-refresh check every 30 seconds
        setTimeout(() => {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
