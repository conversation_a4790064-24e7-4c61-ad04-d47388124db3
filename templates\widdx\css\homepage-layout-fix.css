/*!
 * WHMCS WIDDX Theme - Homepage Layout Fix
 * Ensures proper spacing and layout flow for homepage sections
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

/* ===================================
   HOMEPAGE LAYOUT FIXES
   =================================== */

/* Ensure hero section doesn't interfere with content flow */
.modern-hero-section {
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}

/* Add proper spacing between homepage sections */
.homepage-section {
    padding: 60px 0;
    position: relative;
    z-index: 2;
}

/* Ensure sections don't overlap with hero */
.homepage-section:first-of-type {
    margin-top: 0;
    padding-top: 80px;
}

/* Responsive spacing adjustments */
@media (max-width: 768px) {
    .homepage-section {
        padding: 40px 0;
    }
    
    .homepage-section:first-of-type {
        padding-top: 60px;
    }
}

/* Fix for sections appearing behind hero */
.pricing-section,
.hosting-features-section,
.productgroups-section,
.all-in-one-section,
.speed-section,
.tools-section,
.network-section,
.why-widdx-section,
.faq-section,
.announcements-section {
    position: relative;
    z-index: 2;
    background: var(--body-bg, #fff);
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .pricing-section,
[data-bs-theme="dark"] .hosting-features-section,
[data-bs-theme="dark"] .productgroups-section,
[data-bs-theme="dark"] .all-in-one-section,
[data-bs-theme="dark"] .speed-section,
[data-bs-theme="dark"] .tools-section,
[data-bs-theme="dark"] .network-section,
[data-bs-theme="dark"] .why-widdx-section,
[data-bs-theme="dark"] .faq-section,
[data-bs-theme="dark"] .announcements-section {
    background: var(--body-bg-dark, #1a1a1a);
}

/* Ensure footer appears at the bottom */
.widdx-footer {
    position: relative;
    z-index: 3;
    margin-top: 0;
}

/* Fix for any floating elements */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Ensure proper content flow */
.homepage-content-flow {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.homepage-content-flow .modern-hero-section {
    flex: 0 0 auto;
}

.homepage-content-flow .homepage-sections {
    flex: 1 0 auto;
}

.homepage-content-flow .widdx-footer {
    flex: 0 0 auto;
}

/* Debug styles (remove in production) */
.debug-layout .modern-hero-section {
    border: 2px solid red;
}

.debug-layout .homepage-section {
    border: 2px solid blue;
}

.debug-layout .widdx-footer {
    border: 2px solid green;
}

/* Animation improvements */
.homepage-section {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease-out forwards;
}

.homepage-section:nth-child(1) { animation-delay: 0.1s; }
.homepage-section:nth-child(2) { animation-delay: 0.2s; }
.homepage-section:nth-child(3) { animation-delay: 0.3s; }
.homepage-section:nth-child(4) { animation-delay: 0.4s; }
.homepage-section:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .homepage-section {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* Print styles */
@media print {
    .modern-hero-section {
        min-height: auto;
        page-break-inside: avoid;
    }
    
    .homepage-section {
        page-break-inside: avoid;
        padding: 20px 0;
    }
}
