<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:52:22
  from 'C:\xampp\htdocs\templates\widdx\homepage.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68801626703b16_69115078',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '1827928d31be74d7ab37a698f67ce353a96a91aa' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\homepage.tpl',
      1 => 1753224413,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68801626703b16_69115078 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['loggedin']->value) {?>
    <?php echo '<script'; ?>
>
        window.location.href = "<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php";
    <?php echo '</script'; ?>
>
<?php } else { ?>
        
                        <?php }
}
}
