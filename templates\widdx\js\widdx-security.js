/*!
 * WHMCS WIDDX Theme - Security Enhancement Layer
 * Provides additional security features and protections
 * Copyright (c) 2025 WIDDX Development Team
 * Version: 1.0.0
 */

(function(window, document) {
    'use strict';

    const WiddxSecurity = {
        
        // Content Security Policy enforcement
        enforceCSP: function() {
            // Prevent inline script execution from untrusted sources
            const scripts = document.querySelectorAll('script:not([src])');
            scripts.forEach(script => {
                if (!script.hasAttribute('data-widdx-trusted')) {
                    console.warn('Untrusted inline script detected and blocked');
                    script.remove();
                }
            });
        },

        // XSS Protection
        sanitizeInput: function(input) {
            const div = document.createElement('div');
            div.textContent = input;
            return div.innerHTML;
        },

        // Enhanced CSRF protection
        validateCSRFToken: function() {
            const token = document.querySelector('meta[name="csrf-token"]');
            if (!token) {
                console.warn('CSRF token not found');
                return false;
            }
            return token.getAttribute('content');
        },

        // Secure form submission
        secureFormSubmit: function(form) {
            const csrfToken = this.validateCSRFToken();
            if (!csrfToken) {
                return false;
            }

            // Add CSRF token to form if not present
            let csrfInput = form.querySelector('input[name="_token"]');
            if (!csrfInput) {
                csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
            }

            // Sanitize form inputs
            const inputs = form.querySelectorAll('input[type="text"], textarea');
            inputs.forEach(input => {
                if (input.value) {
                    input.value = this.sanitizeInput(input.value);
                }
            });

            return true;
        },

        // Click-jacking protection
        preventClickjacking: function() {
            if (window.top !== window.self) {
                console.warn('Potential clickjacking attempt detected');
                window.top.location = window.self.location;
            }
        },

        // Session security
        enhanceSessionSecurity: function() {
            // Check for session timeout
            const lastActivity = localStorage.getItem('widdx_last_activity');
            const now = Date.now();
            const timeout = 30 * 60 * 1000; // 30 minutes

            if (lastActivity && (now - parseInt(lastActivity)) > timeout) {
                console.warn('Session timeout detected');
                this.handleSessionTimeout();
            } else {
                localStorage.setItem('widdx_last_activity', now.toString());
            }

            // Update activity on user interaction
            ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
                document.addEventListener(event, this.throttle(() => {
                    localStorage.setItem('widdx_last_activity', Date.now().toString());
                }, 60000)); // Update every minute
            });
        },

        // Handle session timeout
        handleSessionTimeout: function() {
            // Clear sensitive data
            localStorage.removeItem('widdx_last_activity');
            sessionStorage.clear();
            
            // Redirect to login if not already there
            if (!window.location.pathname.includes('login')) {
                window.location.href = '/login.php?timeout=1';
            }
        },

        // Input validation
        validateInput: function(input, type) {
            switch (type) {
                case 'email':
                    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
                case 'phone':
                    return /^[\+]?[1-9][\d]{0,15}$/.test(input.replace(/\s/g, ''));
                case 'alphanumeric':
                    return /^[a-zA-Z0-9]+$/.test(input);
                case 'domain':
                    return /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(input);
                default:
                    return true;
            }
        },

        // Rate limiting for API calls
        rateLimiter: {
            requests: new Map(),
            
            isAllowed: function(key, limit = 10, window = 60000) {
                const now = Date.now();
                const requests = this.requests.get(key) || [];
                
                // Remove old requests outside the window
                const validRequests = requests.filter(time => now - time < window);
                
                if (validRequests.length >= limit) {
                    return false;
                }
                
                validRequests.push(now);
                this.requests.set(key, validRequests);
                return true;
            }
        },

        // Secure cookie handling
        secureCookie: {
            set: function(name, value, days = 7) {
                const expires = new Date();
                expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
                
                document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; secure; samesite=strict`;
            },
            
            get: function(name) {
                const nameEQ = name + "=";
                const ca = document.cookie.split(';');
                
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                    if (c.indexOf(nameEQ) === 0) {
                        return decodeURIComponent(c.substring(nameEQ.length, c.length));
                    }
                }
                return null;
            },
            
            delete: function(name) {
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
            }
        },

        // Throttle function for performance and security
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // Initialize security features
        init: function() {
            this.preventClickjacking();
            this.enforceCSP();
            this.enhanceSessionSecurity();
            
            // Secure all forms
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', (e) => {
                    if (!this.secureFormSubmit(form)) {
                        e.preventDefault();
                        console.error('Form submission blocked due to security validation failure');
                    }
                });
            });

            // Add security headers to AJAX requests
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const [url, options = {}] = args;
                
                // Add security headers
                options.headers = {
                    ...options.headers,
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': WiddxSecurity.validateCSRFToken() || ''
                };
                
                return originalFetch(url, options);
            };

            console.log('WIDDX Security Layer initialized');
        }
    };

    // Make WiddxSecurity globally available
    window.WiddxSecurity = WiddxSecurity;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => WiddxSecurity.init());
    } else {
        WiddxSecurity.init();
    }

})(window, document);
